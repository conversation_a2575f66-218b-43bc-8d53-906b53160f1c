# 字典数据验证功能使用说明

## 功能概述

字典数据验证功能提供了一套完整的解决方案，用于在请求参数校验时根据 `DictDataTypeEnum` 查询 `DictData` 数据来验证参数值的正确性，并支持自定义分隔符处理多值字段。

## 核心组件

### 1. @DictDataValidation 注解

自定义验证注解，支持以下属性：

- `dictType`: 字典类型枚举（必填）
- `delimiter`: 分隔符，默认为逗号
- `allowEmpty`: 是否允许空值，默认为 true
- `allowDuplicate`: 是否允许重复值，默认为 true
- `trim`: 是否去除前后空格，默认为 true
- `messageTemplate`: 自定义错误消息模板

### 2. DictDataValidator 验证器

实现 JSR-303 Bean Validation 规范的验证器，特点：

- 自动注入 Spring 容器中的服务
- 内置缓存机制，提高验证性能
- 支持多种分隔符和验证规则

### 3. DictDataValidationUtil 工具类

提供编程式验证方法：

- 单个值验证
- 多值验证（支持自定义分隔符）
- 批量验证
- 获取字典有效值

### 4. DictDataValidationResult 验证结果

详细的验证结果信息：

- 验证是否通过
- 有效值和无效值列表
- 重复值列表
- 错误消息
- 可用的字典值

## 使用方式

### 1. 注解方式验证

```java
@Data
public class UserForm {
    // 单个值验证
    @DictDataValidation(dictType = DictDataTypeEnum.USER_GENDER)
    private String gender;
    
    // 多值验证（逗号分隔）
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_BRAND,
        delimiter = ",",
        allowDuplicate = false,
        messageTemplate = "设备品牌[{invalidValues}]不在有效范围内"
    )
    private String equipmentBrands;
    
    // 自定义分隔符验证
    @DictDataValidation(
        dictType = DictDataTypeEnum.SAMPLE_TYPE,
        delimiter = ";",
        allowEmpty = false
    )
    private String sampleTypes;
}
```

### 2. 编程式验证

```java
@Autowired
private DictDataValidationUtil dictDataValidationUtil;

// 单个值验证
DictDataValidationResult result = dictDataValidationUtil.validateSingle(
    "男", DictDataTypeEnum.USER_GENDER);

// 多值验证
DictDataValidationResult result = dictDataValidationUtil.validateMultiple(
    "品牌1,品牌2,品牌3", DictDataTypeEnum.EQUIPMENT_BRAND, ",");

// 完整参数验证
DictDataValidationResult result = dictDataValidationUtil.validate(
    "方法1|方法2", DictDataTypeEnum.INSPECT_METHOD, "|", true, false, true);

// 批量验证
Map<String, String> fieldValues = new HashMap<>();
fieldValues.put("gender", "男");
fieldValues.put("status", "启用");

Map<String, DictDataTypeEnum> fieldDictTypes = new HashMap<>();
fieldDictTypes.put("gender", DictDataTypeEnum.USER_GENDER);
fieldDictTypes.put("status", DictDataTypeEnum.USER_STATUS);

Map<String, DictDataValidationResult> results = 
    dictDataValidationUtil.batchValidate(fieldValues, fieldDictTypes, ",");
```

### 3. 控制器中使用

```java
@RestController
public class UserController {
    
    // 注解验证
    @PostMapping("/users")
    public PcsResult<String> createUser(@Valid @RequestBody UserForm form) {
        // 验证通过后的业务逻辑
        return PcsResult.success("用户创建成功");
    }
    
    // 编程式验证
    @PostMapping("/validate")
    public PcsResult<DictDataValidationResult> validate(@RequestParam String value) {
        DictDataValidationResult result = dictDataValidationUtil.validateSingle(
            value, DictDataTypeEnum.USER_GENDER);
        return PcsResult.success(result);
    }
}
```

## 支持的分隔符

工具类提供了常用分隔符常量：

- `DELIMITER_COMMA = ","` - 逗号
- `DELIMITER_SEMICOLON = ";"` - 分号  
- `DELIMITER_PIPE = "|"` - 竖线
- `DELIMITER_SLASH = "/"` - 斜杠
- `DELIMITER_SPACE = " "` - 空格

## 错误消息模板

支持以下占位符：

- `{dictType}` - 字典类型名称
- `{invalidValues}` - 无效值列表
- `{validValues}` - 有效值列表

示例：
```java
@DictDataValidation(
    dictType = DictDataTypeEnum.USER_GENDER,
    messageTemplate = "性别[{invalidValues}]无效，可选值：{validValues}"
)
```

## 性能优化

1. **缓存机制**: 验证器内置缓存，避免重复查询数据库
2. **缓存过期**: 缓存有效期为5分钟，确保数据时效性
3. **批量查询**: 一次查询获取所有字典数据，减少数据库访问

## 注意事项

1. 确保 Spring 容器中已注册 `DictDataService`
2. 字典数据查询基于 `parent_id > 0` 的条件
3. 只验证有效且启用的字典数据
4. 空值处理根据 `allowEmpty` 参数决定
5. 重复值检查根据 `allowDuplicate` 参数决定

## 测试示例

可以通过以下 API 进行测试：

- `POST /api/dict-validation-example/annotation-validation` - 注解验证
- `POST /api/dict-validation-example/programmatic-validation` - 编程式验证
- `POST /api/dict-validation-example/batch-validation` - 批量验证
- `GET /api/dict-validation-example/valid-values/{dictType}` - 获取有效值
- `GET /api/dict-validation-example/validate-single` - 单值验证
- `GET /api/dict-validation-example/validate-multiple` - 多值验证
