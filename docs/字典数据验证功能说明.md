# 字典数据验证功能说明

## 功能概述

字典数据验证功能提供了类似 `@NotNull`、`@NotBlank` 的JSR-303验证注解，用于验证字段值是否在指定的字典数据中存在。

## 核心组件

### 1. @DictDataValidation 注解

自定义验证注解，支持以下属性：

- `dictType`: 字典类型枚举（必填）
- `delimiter`: 分隔符，默认为逗号
- `allowEmpty`: 是否允许空值，默认为 true
- `allowDuplicate`: 是否允许重复值，默认为 true
- `trim`: 是否去除前后空格，默认为 true
- `messageTemplate`: 自定义错误消息模板

### 2. DictDataValidator 验证器

实现 JSR-303 Bean Validation 规范的验证器，使用复杂SQL查询验证字典数据有效性。

### 3. DictDataValidationUtil 工具类

提供编程式验证方法：

- `isValidDictValue(String value, DictDataTypeEnum dictType)`: 验证单个值
- `getValidDictValues(DictDataTypeEnum dictType)`: 获取字典有效值列表
- `parseValues(String value, String delimiter, boolean trim)`: 解析输入值

## 使用方式

### 注解方式验证

```java
@Data
public class UserForm {
    // 单个值验证
    @DictDataValidation(dictType = DictDataTypeEnum.USER_GENDER)
    private String gender;
    
    // 多值验证（逗号分隔）
    @DictDataValidation(
        dictType = DictDataTypeEnum.EQUIPMENT_BRAND,
        delimiter = ",",
        allowDuplicate = false,
        messageTemplate = "设备品牌[{invalidValues}]不在有效范围内"
    )
    private String equipmentBrands;
    
    // 自定义分隔符验证
    @DictDataValidation(
        dictType = DictDataTypeEnum.SAMPLE_TYPE,
        delimiter = ";",
        allowEmpty = false
    )
    private String sampleTypes;
}
```

### 编程式验证

```java
@Resource
private DictDataValidationUtil dictDataValidationUtil;

// 验证单个值
boolean isValid = dictDataValidationUtil.isValidDictValue("0", DictDataTypeEnum.USER_GENDER);

// 获取有效值列表
List<String> validValues = dictDataValidationUtil.getValidDictValues(DictDataTypeEnum.USER_GENDER);

// 解析多值字符串
List<String> values = dictDataValidationUtil.parseValues("值1,值2,值3", ",", true);
```

### 控制器中使用

```java
@RestController
public class UserController {
    
    @PostMapping("/users")
    public PcsResult<String> createUser(@Valid @RequestBody UserForm form) {
        // 验证通过后的业务逻辑
        return PcsResult.success("用户创建成功");
    }
}
```

## 查询逻辑

验证器使用以下SQL查询逻辑：

```sql
SELECT dict_value FROM t_dict_data 
WHERE dict_value = '输入值'
  AND is_effect = 0 
  AND status = 1
  AND EXISTS (
    SELECT 1 FROM t_dict_data parent 
    WHERE parent.dict_value = '字典类型code'
      AND parent.is_effect = 0 
      AND parent.status = 1 
      AND t_dict_data.parent_id = parent.id
  )
```

## 错误消息模板

支持以下占位符：

- `{dictType}` - 字典类型名称
- `{invalidValues}` - 无效值列表
- `{validValues}` - 有效值列表

## 注意事项

1. 确保 Spring 容器中已注册 `DictDataMapper`
2. 字典数据验证基于父子级关系，使用EXISTS子查询
3. 只验证有效且启用的字典数据（`is_effect=0`, `status=1`）
4. 输入值与字典数据的 `dict_value` 字段进行匹配
5. 空值处理根据 `allowEmpty` 参数决定
6. 重复值检查根据 `allowDuplicate` 参数决定
