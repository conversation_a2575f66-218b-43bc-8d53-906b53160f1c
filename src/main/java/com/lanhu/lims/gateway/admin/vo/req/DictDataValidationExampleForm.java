package com.lanhu.lims.gateway.admin.vo.req;

import com.lanhu.lims.gateway.admin.annotation.DictDataValidation;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/********************************
 * @title DictDataValidationExampleForm
 * @package com.lanhu.lims.gateway.admin.vo.req
 * @description 字典数据验证使用示例表单
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@ApiModel(description = "字典数据验证示例表单")
@Data
public class DictDataValidationExampleForm {
    /**
     * 用户性别（单个值验证）
     * 验证输入值是否在用户性别字典中存在
     */
    @ApiModelProperty(value = "用户性别")
    @DictDataValidation(dictType = DictDataTypeEnum.USER_GENDER,
                       message = "用户性别值无效")
    private String gender;

    /**
     * 执行标准类别（单个值验证）
     * 验证输入值是否在执行标准类别字典中存在
     */
    @ApiModelProperty(value = "执行标准类别")
    @DictDataValidation(dictType = DictDataTypeEnum.EXECUTION_STANDARD_CATEGORY,
                       message = "执行标准类别值无效")
    private String standardCategory;

    /**
     * 设备品牌（逗号分隔多值验证）
     */
    @ApiModelProperty(value = "设备品牌，多个用逗号分隔")
    @DictDataValidation(dictType = DictDataTypeEnum.EQUIPMENT_BRAND,
                       delimiter = ",",
                       allowDuplicate = false,
                       messageTemplate = "设备品牌[{invalidValues}]不在有效范围内")
    private String equipmentBrands;

    /**
     * 样本类型（分号分隔多值验证）
     */
    @ApiModelProperty(value = "样本类型，多个用分号分隔")
    @DictDataValidation(dictType = DictDataTypeEnum.SAMPLE_TYPE,
                       delimiter = ";",
                       allowEmpty = false,
                       allowDuplicate = false,
                       messageTemplate = "样本类型验证失败，无效值：{invalidValues}")
    private String sampleTypes;

    /**
     * 检测方法（竖线分隔多值验证）
     */
    @ApiModelProperty(value = "检测方法，多个用竖线分隔")
    @DictDataValidation(dictType = DictDataTypeEnum.INSPECT_METHOD,
                       delimiter = "|",
                       trim = true,
                       messageTemplate = "检测方法[{invalidValues}]无效，可选值：{validValues}")
    private String inspectMethods;

    /**
     * 试剂品牌（斜杠分隔多值验证）
     */
    @ApiModelProperty(value = "试剂品牌，多个用斜杠分隔")
    @DictDataValidation(dictType = DictDataTypeEnum.REAGENT_BRAND,
                       delimiter = "/",
                       allowEmpty = true,
                       allowDuplicate = true)
    private String reagentBrands;

    /**
     * 用户状态（必填字段）
     */
    @ApiModelProperty(value = "用户状态")
    @NotBlank(message = "用户状态不能为空")
    @DictDataValidation(dictType = DictDataTypeEnum.USER_STATUS,
                       allowEmpty = false,
                       messageTemplate = "用户状态[{invalidValues}]无效")
    private String userStatus;

    /**
     * 部门类型（空格分隔多值验证）
     */
    @ApiModelProperty(value = "部门类型，多个用空格分隔")
    @DictDataValidation(dictType = DictDataTypeEnum.DEPT_TYPE,
                       delimiter = " ",
                       trim = true,
                       allowDuplicate = false)
    private String deptTypes;
}
