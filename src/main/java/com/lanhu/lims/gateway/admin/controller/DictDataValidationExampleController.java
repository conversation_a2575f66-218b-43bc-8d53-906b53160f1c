package com.lanhu.lims.gateway.admin.controller;

import com.lanhu.lims.gateway.admin.core.PcsResult;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.utils.validation.DictDataValidationResult;
import com.lanhu.lims.gateway.admin.utils.validation.DictDataValidationUtil;
import com.lanhu.lims.gateway.admin.vo.req.DictDataValidationExampleForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/********************************
 * @title DictDataValidationExampleController
 * @package com.lanhu.lims.gateway.admin.controller
 * @description 字典数据验证示例控制器
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Api(tags = "字典数据验证示例")
@RestController
@RequestMapping("/api/dict-validation-example")
@Slf4j
@Validated
public class DictDataValidationExampleController {
    @Autowired
    private DictDataValidationUtil dictDataValidationUtil;

    /**
     * 注解方式验证示例
     */
    @ApiOperation("注解方式验证示例")
    @PostMapping("/annotation-validation")
    public PcsResult<String> annotationValidation(@Valid @RequestBody DictDataValidationExampleForm form) {
        log.info("注解验证通过，表单数据：{}", form);
        return PcsResult.success("注解验证通过");
    }

    /**
     * 编程式验证示例
     */
    @ApiOperation("编程式验证示例")
    @PostMapping("/programmatic-validation")
    public PcsResult<Map<String, Object>> programmaticValidation(@RequestBody Map<String, String> params) {
        Map<String, Object> result = new HashMap<>();

        // 单个值验证
        String gender = params.get("gender");
        DictDataValidationResult genderResult = dictDataValidationUtil.validateSingle(gender, DictDataTypeEnum.USER_GENDER);
        result.put("genderValidation", genderResult);

        // 多个值验证（逗号分隔）
        String equipmentBrands = params.get("equipmentBrands");
        DictDataValidationResult brandsResult = dictDataValidationUtil.validateMultiple(equipmentBrands, DictDataTypeEnum.EQUIPMENT_BRAND);
        result.put("equipmentBrandsValidation", brandsResult);

        // 自定义分隔符验证
        String sampleTypes = params.get("sampleTypes");
        DictDataValidationResult typesResult = dictDataValidationUtil.validateMultiple(sampleTypes, DictDataTypeEnum.SAMPLE_TYPE, ";");
        result.put("sampleTypesValidation", typesResult);

        // 完整参数验证
        String inspectMethods = params.get("inspectMethods");
        DictDataValidationResult methodsResult = dictDataValidationUtil.validate(
                inspectMethods, DictDataTypeEnum.INSPECT_METHOD, "|", true, false, true);
        result.put("inspectMethodsValidation", methodsResult);

        return PcsResult.success(result);
    }

    /**
     * 批量验证示例
     */
    @ApiOperation("批量验证示例")
    @PostMapping("/batch-validation")
    public PcsResult<Map<String, Object>> batchValidation(@RequestBody Map<String, String> params) {
        // 定义字段与字典类型的映射
        Map<String, DictDataTypeEnum> fieldDictTypes = new HashMap<>();
        fieldDictTypes.put("gender", DictDataTypeEnum.USER_GENDER);
        fieldDictTypes.put("userStatus", DictDataTypeEnum.USER_STATUS);
        fieldDictTypes.put("deptType", DictDataTypeEnum.DEPT_TYPE);
        fieldDictTypes.put("equipmentBrand", DictDataTypeEnum.EQUIPMENT_BRAND);

        // 批量验证
        Map<String, DictDataValidationResult> validationResults = 
                dictDataValidationUtil.batchValidate(params, fieldDictTypes, ",");

        // 检查是否所有验证都通过
        boolean allValid = dictDataValidationUtil.isAllValid(validationResults);
        List<String> invalidFields = dictDataValidationUtil.getInvalidFields(validationResults);

        Map<String, Object> result = new HashMap<>();
        result.put("allValid", allValid);
        result.put("invalidFields", invalidFields);
        result.put("validationResults", validationResults);

        return PcsResult.success(result);
    }

    /**
     * 获取字典有效值示例
     */
    @ApiOperation("获取字典有效值示例")
    @GetMapping("/valid-values/{dictType}")
    public PcsResult<List<String>> getValidValues(@PathVariable String dictType) {
        try {
            DictDataTypeEnum dictTypeEnum = DictDataTypeEnum.getByCode(dictType);
            if (dictTypeEnum == null) {
                return PcsResult.error("字典类型不存在：" + dictType);
            }

            List<String> validValues = dictDataValidationUtil.getValidDictValues(dictTypeEnum);
            return PcsResult.success(validValues);
        } catch (Exception e) {
            log.error("获取字典有效值异常", e);
            return PcsResult.error("获取字典有效值异常：" + e.getMessage());
        }
    }

    /**
     * 验证单个值示例
     */
    @ApiOperation("验证单个值示例")
    @GetMapping("/validate-single")
    public PcsResult<DictDataValidationResult> validateSingle(
            @RequestParam String value,
            @RequestParam String dictType) {
        try {
            DictDataTypeEnum dictTypeEnum = DictDataTypeEnum.getByCode(dictType);
            if (dictTypeEnum == null) {
                return PcsResult.error("字典类型不存在：" + dictType);
            }

            DictDataValidationResult result = dictDataValidationUtil.validateSingle(value, dictTypeEnum);
            return PcsResult.success(result);
        } catch (Exception e) {
            log.error("验证单个值异常", e);
            return PcsResult.error("验证单个值异常：" + e.getMessage());
        }
    }

    /**
     * 验证多个值示例
     */
    @ApiOperation("验证多个值示例")
    @GetMapping("/validate-multiple")
    public PcsResult<DictDataValidationResult> validateMultiple(
            @RequestParam String values,
            @RequestParam String dictType,
            @RequestParam(defaultValue = ",") String delimiter) {
        try {
            DictDataTypeEnum dictTypeEnum = DictDataTypeEnum.getByCode(dictType);
            if (dictTypeEnum == null) {
                return PcsResult.error("字典类型不存在：" + dictType);
            }

            DictDataValidationResult result = dictDataValidationUtil.validateMultiple(values, dictTypeEnum, delimiter);
            return PcsResult.success(result);
        } catch (Exception e) {
            log.error("验证多个值异常", e);
            return PcsResult.error("验证多个值异常：" + e.getMessage());
        }
    }
}
