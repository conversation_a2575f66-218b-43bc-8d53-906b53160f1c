package com.lanhu.lims.gateway.admin.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.annotation.DictDataValidation;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.service.DictDataService;
import com.lanhu.lims.gateway.admin.utils.validation.DictDataValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/********************************
 * @title DictDataValidator
 * @package com.lanhu.lims.gateway.admin.validation
 * @description 字典数据验证器实现
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Slf4j
@Component
public class DictDataValidator implements ConstraintValidator<DictDataValidation, String> {
    @Autowired
    private DictDataService dictDataService;

    @Autowired
    private DictDataValidationUtil dictDataValidationUtil;

    /**
     * 字典数据缓存，key为字典类型code，value为字典值列表
     */
    private static final Map<String, List<String>> DICT_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存过期时间（毫秒）
     */
    private static final long CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

    /**
     * 缓存时间戳
     */
    private static final Map<String, Long> CACHE_TIMESTAMP = new ConcurrentHashMap<>();

    private DictDataValidation annotation;

    @Override
    public void initialize(DictDataValidation annotation) {
        this.annotation = annotation;
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 空值处理
        if (StrUtil.isBlank(value)) {
            return annotation.allowEmpty();
        }

        try {
            // 获取字典类型
            DictDataTypeEnum dictType = annotation.dictType();
            String delimiter = annotation.delimiter();
            boolean allowDuplicate = annotation.allowDuplicate();
            boolean trim = annotation.trim();

            // 获取有效的字典值
            List<String> validDictValues = getValidDictValues(dictType);
            if (CollUtil.isEmpty(validDictValues)) {
                log.warn("字典类型[{}]没有有效的字典数据", dictType.getName());
                return false;
            }

            // 解析输入值
            List<String> inputValues = parseInputValues(value, delimiter, trim);
            if (CollUtil.isEmpty(inputValues)) {
                return annotation.allowEmpty();
            }

            // 检查重复值
            if (!allowDuplicate && hasDuplicateValues(inputValues)) {
                setCustomErrorMessage(context, "字段值包含重复项");
                return false;
            }

            // 验证每个值是否在字典中
            List<String> invalidValues = inputValues.stream()
                    .filter(inputValue -> !validDictValues.contains(inputValue))
                    .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(invalidValues)) {
                String errorMessage = buildErrorMessage(dictType, invalidValues, validDictValues);
                setCustomErrorMessage(context, errorMessage);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("字典数据验证异常", e);
            setCustomErrorMessage(context, "字典数据验证异常：" + e.getMessage());
            return false;
        }
    }

    /**
     * 获取有效的字典值列表
     */
    private List<String> getValidDictValues(DictDataTypeEnum dictType) {
        String dictTypeCode = dictType.getCode();
        
        // 检查缓存
        if (isCacheValid(dictTypeCode)) {
            return DICT_CACHE.get(dictTypeCode);
        }

        // 查询字典数据
        List<DictData> dictDataList = dictDataService.list();

        // 首先找到字典类型的父级数据
        DictData parentDict = dictDataList.stream()
                .filter(dictData -> dictTypeCode.equals(dictData.getDictValue()) && dictData.getParentId() == 0)
                .findFirst()
                .orElse(null);

        if (parentDict == null) {
            return Collections.emptyList();
        }

        // 然后查找该字典类型下的所有子级数据
        List<String> validValues = dictDataList.stream()
                .filter(dictData -> parentDict.getId().equals(dictData.getParentId()))
                .map(DictData::getDictLabel)
                .collect(Collectors.toList());

        // 更新缓存
        DICT_CACHE.put(dictTypeCode, validValues);
        CACHE_TIMESTAMP.put(dictTypeCode, System.currentTimeMillis());

        return validValues;
    }

    /**
     * 检查缓存是否有效
     */
    private boolean isCacheValid(String dictTypeCode) {
        if (!DICT_CACHE.containsKey(dictTypeCode)) {
            return false;
        }
        Long timestamp = CACHE_TIMESTAMP.get(dictTypeCode);
        return timestamp != null && (System.currentTimeMillis() - timestamp) < CACHE_EXPIRE_TIME;
    }

    /**
     * 解析输入值
     */
    private List<String> parseInputValues(String value, String delimiter, boolean trim) {
        return dictDataValidationUtil.parseValues(value, delimiter, trim);
    }

    /**
     * 检查是否有重复值
     */
    private boolean hasDuplicateValues(List<String> values) {
        return values.size() != values.stream().distinct().count();
    }

    /**
     * 构建错误消息
     */
    private String buildErrorMessage(DictDataTypeEnum dictType, List<String> invalidValues, List<String> validValues) {
        String template = annotation.messageTemplate();
        return template.replace("{dictType}", dictType.getName())
                .replace("{invalidValues}", String.join(",", invalidValues))
                .replace("{validValues}", String.join(",", validValues));
    }

    /**
     * 设置自定义错误消息
     */
    private void setCustomErrorMessage(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

    /**
     * 清除缓存
     */
    public static void clearCache() {
        DICT_CACHE.clear();
        CACHE_TIMESTAMP.clear();
    }

    /**
     * 清除指定字典类型的缓存
     */
    public static void clearCache(String dictTypeCode) {
        DICT_CACHE.remove(dictTypeCode);
        CACHE_TIMESTAMP.remove(dictTypeCode);
    }
}
