package com.lanhu.lims.gateway.admin.validation.dict;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.*;

/********************************
 * @title DictDataValidator
 * @package com.lanhu.lims.gateway.admin.validation
 * @description 字典数据验证器实现
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Slf4j
@Component
public class DictDataValidator implements ConstraintValidator<DictDataValidation, String> {
    @Resource
    private DictDataMapper dictDataMapper;

    @Resource
    private DictDataValidationUtil dictDataValidationUtil;


    private DictDataValidation annotation;

    @Override
    public void initialize(DictDataValidation annotation) {
        this.annotation = annotation;
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 空值处理
        if (StrUtil.isBlank(value)) {
            return annotation.allowEmpty();
        }

        try {
            // 获取字典类型
            DictDataTypeEnum dictType = annotation.dictType();
            String delimiter = annotation.delimiter();
            boolean allowDuplicate = annotation.allowDuplicate();
            boolean trim = annotation.trim();

            // 解析输入值
            List<String> inputValues = parseInputValues(value, delimiter, trim);
            if (CollUtil.isEmpty(inputValues)) {
                return annotation.allowEmpty();
            }

            // 检查重复值
            if (!allowDuplicate && hasDuplicateValues(inputValues)) {
                setCustomErrorMessage(context, "字段值包含重复项");
                return false;
            }

            // 验证每个值是否在字典中
            List<String> invalidValues = new ArrayList<>();
            for (String inputValue : inputValues) {
                if (!dictDataValidationUtil.isValidDictValue(inputValue, dictType)) {
                    invalidValues.add(inputValue);
                }
            }

            if (CollUtil.isNotEmpty(invalidValues)) {
                String errorMessage = buildErrorMessage(dictType, invalidValues);
                setCustomErrorMessage(context, errorMessage);
                return false;
            }

            return true;
        } catch (Exception e) {
            log.error("字典数据验证异常", e);
            setCustomErrorMessage(context, "字典数据验证异常：" + e.getMessage());
            return false;
        }
    }



    /**
     * 解析输入值
     */
    private List<String> parseInputValues(String value, String delimiter, boolean trim) {
        return dictDataValidationUtil.parseValues(value, delimiter, trim);
    }

    /**
     * 检查是否有重复值
     */
    private boolean hasDuplicateValues(List<String> values) {
        return values.size() != values.stream().distinct().count();
    }

    /**
     * 构建错误消息
     */
    private String buildErrorMessage(DictDataTypeEnum dictType, List<String> invalidValues) {
        String template = annotation.messageTemplate();
        List<String> validValues = dictDataValidationUtil.getValidDictValues(dictType);
        return template.replace("{dictType}", dictType.getName())
                .replace("{invalidValues}", String.join(",", invalidValues))
                .replace("{validValues}", String.join(",", validValues));
    }

    /**
     * 设置自定义错误消息
     */
    private void setCustomErrorMessage(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }


}
