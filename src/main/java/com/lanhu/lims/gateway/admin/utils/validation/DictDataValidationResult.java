package com.lanhu.lims.gateway.admin.utils.validation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/********************************
 * @title DictDataValidationResult
 * @package com.lanhu.lims.gateway.admin.utils.validation
 * @description 字典数据验证结果
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DictDataValidationResult {
    /**
     * 验证是否通过
     */
    private boolean valid;

    /**
     * 原始输入值
     */
    private String originalValue;

    /**
     * 解析后的值列表
     */
    private List<String> parsedValues;

    /**
     * 有效的值列表
     */
    private List<String> validValues;

    /**
     * 无效的值列表
     */
    private List<String> invalidValues;

    /**
     * 重复的值列表
     */
    private List<String> duplicateValues;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 字典类型名称
     */
    private String dictTypeName;

    /**
     * 所有可用的字典值
     */
    private List<String> availableValues;

    /**
     * 创建成功的验证结果
     */
    public static DictDataValidationResult success(String originalValue, List<String> parsedValues, 
                                                   List<String> validValues, String dictTypeName, 
                                                   List<String> availableValues) {
        DictDataValidationResult result = new DictDataValidationResult();
        result.setValid(true);
        result.setOriginalValue(originalValue);
        result.setParsedValues(parsedValues);
        result.setValidValues(validValues);
        result.setDictTypeName(dictTypeName);
        result.setAvailableValues(availableValues);
        return result;
    }

    /**
     * 创建失败的验证结果
     */
    public static DictDataValidationResult failure(String originalValue, List<String> parsedValues,
                                                    List<String> validValues, List<String> invalidValues,
                                                    List<String> duplicateValues, String errorMessage,
                                                    String dictTypeName, List<String> availableValues) {
        DictDataValidationResult result = new DictDataValidationResult();
        result.setValid(false);
        result.setOriginalValue(originalValue);
        result.setParsedValues(parsedValues);
        result.setValidValues(validValues);
        result.setInvalidValues(invalidValues);
        result.setDuplicateValues(duplicateValues);
        result.setErrorMessage(errorMessage);
        result.setDictTypeName(dictTypeName);
        result.setAvailableValues(availableValues);
        return result;
    }

    /**
     * 是否有无效值
     */
    public boolean hasInvalidValues() {
        return invalidValues != null && !invalidValues.isEmpty();
    }

    /**
     * 是否有重复值
     */
    public boolean hasDuplicateValues() {
        return duplicateValues != null && !duplicateValues.isEmpty();
    }
}
