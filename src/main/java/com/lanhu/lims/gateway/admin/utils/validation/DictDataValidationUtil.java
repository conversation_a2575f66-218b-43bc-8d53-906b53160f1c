package com.lanhu.lims.gateway.admin.utils.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.model.DictData;
import com.lanhu.lims.gateway.admin.service.DictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/********************************
 * @title DictDataValidationUtil
 * @package com.lanhu.lims.gateway.admin.utils.validation
 * @description 字典数据验证工具类，提供编程式验证方法
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Slf4j
@Component
public class DictDataValidationUtil {
    @Autowired
    private DictDataService dictDataService;

    /**
     * 常用分隔符
     */
    public static final String DELIMITER_COMMA = ",";
    public static final String DELIMITER_SEMICOLON = ";";
    public static final String DELIMITER_PIPE = "|";
    public static final String DELIMITER_SLASH = "/";
    public static final String DELIMITER_SPACE = " ";

    /**
     * 验证单个字典值
     */
    public DictDataValidationResult validateSingle(String value, DictDataTypeEnum dictType) {
        return validate(value, dictType, DELIMITER_COMMA, true, true, true);
    }

    /**
     * 验证多个字典值（逗号分隔）
     */
    public DictDataValidationResult validateMultiple(String values, DictDataTypeEnum dictType) {
        return validate(values, dictType, DELIMITER_COMMA, true, true, true);
    }

    /**
     * 验证多个字典值（自定义分隔符）
     */
    public DictDataValidationResult validateMultiple(String values, DictDataTypeEnum dictType, String delimiter) {
        return validate(values, dictType, delimiter, true, true, true);
    }

    /**
     * 完整的字典数据验证
     */
    public DictDataValidationResult validate(String value, DictDataTypeEnum dictType, String delimiter,
                                             boolean allowEmpty, boolean allowDuplicate, boolean trim) {
        try {
            // 空值处理
            if (StrUtil.isBlank(value)) {
                if (allowEmpty) {
                    return DictDataValidationResult.success(value, Collections.emptyList(), 
                            Collections.emptyList(), dictType.getName(), Collections.emptyList());
                } else {
                    return DictDataValidationResult.failure(value, Collections.emptyList(),
                            Collections.emptyList(), Collections.emptyList(), Collections.emptyList(),
                            "字段值不能为空", dictType.getName(), Collections.emptyList());
                }
            }

            // 获取有效的字典值
            List<String> availableValues = getValidDictValues(dictType);
            if (CollUtil.isEmpty(availableValues)) {
                return DictDataValidationResult.failure(value, Collections.emptyList(),
                        Collections.emptyList(), Collections.emptyList(), Collections.emptyList(),
                        "字典类型[" + dictType.getName() + "]没有有效的字典数据", 
                        dictType.getName(), Collections.emptyList());
            }

            // 解析输入值
            List<String> parsedValues = parseValues(value, delimiter, trim);
            if (CollUtil.isEmpty(parsedValues)) {
                if (allowEmpty) {
                    return DictDataValidationResult.success(value, parsedValues, 
                            Collections.emptyList(), dictType.getName(), availableValues);
                } else {
                    return DictDataValidationResult.failure(value, parsedValues,
                            Collections.emptyList(), Collections.emptyList(), Collections.emptyList(),
                            "解析后的字段值为空", dictType.getName(), availableValues);
                }
            }

            // 检查重复值
            List<String> duplicateValues = findDuplicateValues(parsedValues);
            if (!allowDuplicate && CollUtil.isNotEmpty(duplicateValues)) {
                return DictDataValidationResult.failure(value, parsedValues,
                        Collections.emptyList(), Collections.emptyList(), duplicateValues,
                        "字段值包含重复项：" + String.join(",", duplicateValues), 
                        dictType.getName(), availableValues);
            }

            // 验证每个值是否在字典中
            List<String> validValues = new ArrayList<>();
            List<String> invalidValues = new ArrayList<>();

            for (String parsedValue : parsedValues) {
                if (availableValues.contains(parsedValue)) {
                    validValues.add(parsedValue);
                } else {
                    invalidValues.add(parsedValue);
                }
            }

            if (CollUtil.isNotEmpty(invalidValues)) {
                String errorMessage = String.format("字段值不在字典[%s]的有效范围内，无效值：%s，有效值：%s",
                        dictType.getName(), String.join(",", invalidValues), String.join(",", availableValues));
                return DictDataValidationResult.failure(value, parsedValues, validValues, invalidValues,
                        duplicateValues, errorMessage, dictType.getName(), availableValues);
            }

            return DictDataValidationResult.success(value, parsedValues, validValues, 
                    dictType.getName(), availableValues);

        } catch (Exception e) {
            log.error("字典数据验证异常", e);
            return DictDataValidationResult.failure(value, Collections.emptyList(),
                    Collections.emptyList(), Collections.emptyList(), Collections.emptyList(),
                    "字典数据验证异常：" + e.getMessage(), dictType.getName(), Collections.emptyList());
        }
    }

    /**
     * 解析输入值
     */
    public List<String> parseValues(String value, String delimiter, boolean trim) {
        if (StrUtil.isBlank(value)) {
            return Collections.emptyList();
        }

        String[] parts = value.split(delimiter);
        List<String> result = new ArrayList<>();

        for (String part : parts) {
            String processedPart = trim ? part.trim() : part;
            if (StrUtil.isNotBlank(processedPart)) {
                result.add(processedPart);
            }
        }

        return result;
    }

    /**
     * 查找重复值
     */
    public List<String> findDuplicateValues(List<String> values) {
        if (CollUtil.isEmpty(values)) {
            return Collections.emptyList();
        }

        Set<String> seen = new HashSet<>();
        Set<String> duplicates = new HashSet<>();

        for (String value : values) {
            if (!seen.add(value)) {
                duplicates.add(value);
            }
        }

        return new ArrayList<>(duplicates);
    }

    /**
     * 获取有效的字典值列表
     */
    public List<String> getValidDictValues(DictDataTypeEnum dictType) {
        try {
            List<DictData> dictDataList = dictDataService.list();

            // 首先找到字典类型的父级数据
            DictData parentDict = dictDataList.stream()
                    .filter(dictData -> dictType.getCode().equals(dictData.getDictValue()) && dictData.getParentId() == 0)
                    .findFirst()
                    .orElse(null);

            if (parentDict == null) {
                return Collections.emptyList();
            }

            // 然后查找该字典类型下的所有子级数据
            return dictDataList.stream()
                    .filter(dictData -> parentDict.getId().equals(dictData.getParentId()))
                    .map(DictData::getDictLabel)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取字典数据异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 批量验证多个字段
     */
    public Map<String, DictDataValidationResult> batchValidate(Map<String, String> fieldValues,
                                                               Map<String, DictDataTypeEnum> fieldDictTypes,
                                                               String delimiter) {
        Map<String, DictDataValidationResult> results = new HashMap<>();

        for (Map.Entry<String, String> entry : fieldValues.entrySet()) {
            String fieldName = entry.getKey();
            String fieldValue = entry.getValue();
            DictDataTypeEnum dictType = fieldDictTypes.get(fieldName);

            if (dictType != null) {
                DictDataValidationResult result = validate(fieldValue, dictType, delimiter, true, true, true);
                results.put(fieldName, result);
            }
        }

        return results;
    }

    /**
     * 检查是否所有验证都通过
     */
    public boolean isAllValid(Map<String, DictDataValidationResult> results) {
        return results.values().stream().allMatch(DictDataValidationResult::isValid);
    }

    /**
     * 获取所有验证失败的字段
     */
    public List<String> getInvalidFields(Map<String, DictDataValidationResult> results) {
        return results.entrySet().stream()
                .filter(entry -> !entry.getValue().isValid())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
}
