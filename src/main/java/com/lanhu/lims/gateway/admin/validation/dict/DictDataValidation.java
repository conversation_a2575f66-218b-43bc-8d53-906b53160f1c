package com.lanhu.lims.gateway.admin.validation.dict;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

/********************************
 * @title DictDataValidation
 * @package com.lanhu.lims.gateway.admin.annotation
 * @description 字典数据验证注解，支持根据字典类型验证字段值的有效性
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = DictDataValidator.class)
@Documented
public @interface DictDataValidation {
    /**
     * 错误消息
     */
    String message() default "字典数据验证失败";

    /**
     * 验证分组
     */
    Class<?>[] groups() default {};

    /**
     * 负载
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 字典类型枚举
     */
    DictDataTypeEnum dictType();

    /**
     * 分隔符，用于分割多个值，默认为逗号
     */
    String delimiter() default ",";

    /**
     * 是否允许空值
     */
    boolean allowEmpty() default true;

    /**
     * 是否允许重复值
     */
    boolean allowDuplicate() default true;

    /**
     * 是否去除前后空格
     */
    boolean trim() default true;

    /**
     * 自定义错误消息模板
     * 可用占位符：
     * {dictType} - 字典类型名称
     * {invalidValues} - 无效值列表
     * {validValues} - 有效值列表
     */
    String messageTemplate() default "字段值不在字典[{dictType}]的有效范围内，无效值：{invalidValues}";
}
