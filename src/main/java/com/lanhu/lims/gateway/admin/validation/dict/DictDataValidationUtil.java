package com.lanhu.lims.gateway.admin.validation.dict;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.enums.EnableEnum;
import com.lanhu.lims.gateway.admin.enums.IsEffectEnum;
import com.lanhu.lims.gateway.admin.mapper.DictDataMapper;
import com.lanhu.lims.gateway.admin.model.DictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/********************************
 * @title DictDataValidationUtil
 * @package com.lanhu.lims.gateway.admin.utils.validation
 * @description 字典数据验证工具类，提供编程式验证方法
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@Slf4j
@Component
public class DictDataValidationUtil {
    @Resource
    private DictDataMapper dictDataMapper;

    /**
     * 验证单个字典值是否有效
     */
    public boolean isValidDictValue(String value, DictDataTypeEnum dictType) {
        if (StrUtil.isBlank(value)) {
            return false;
        }

        try {
            // 查询指定值是否在字典中存在
            LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
            wrapper.select(DictData::getId)
                    .eq(DictData::getDictValue, value)
                    .eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode())
                    .eq(DictData::getStatus, EnableEnum.ENABLE.getCode())
                    .exists("SELECT 1 FROM t_dict_data parent WHERE parent.dict_value = '" +
                            dictType.getCode() +
                            "' AND parent.is_effect = " + IsEffectEnum.NORMAL.getCode() +
                            " AND parent.status = " + EnableEnum.ENABLE.getCode() +
                            " AND t_dict_data.parent_id = parent.id");

            DictData dictData = dictDataMapper.selectOne(wrapper);
            return dictData != null;
        } catch (Exception e) {
            log.error("验证字典值异常", e);
            return false;
        }
    }

    /**
     * 解析输入值
     */
    public List<String> parseValues(String value, String delimiter, boolean trim) {
        if (StrUtil.isBlank(value)) {
            return Collections.emptyList();
        }

        String[] parts = value.split(delimiter);
        List<String> result = new ArrayList<>();

        for (String part : parts) {
            String processedPart = trim ? part.trim() : part;
            if (StrUtil.isNotBlank(processedPart)) {
                result.add(processedPart);
            }
        }

        return result;
    }

    /**
     * 获取有效的字典值列表
     */
    public List<String> getValidDictValues(DictDataTypeEnum dictType) {
        try {
            // 查询该字典类型下的所有有效子级数据
            LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
            wrapper.select(DictData::getDictValue)
                    .eq(DictData::getIsEffect, IsEffectEnum.NORMAL.getCode())
                    .eq(DictData::getStatus, EnableEnum.ENABLE.getCode())
                    .exists("SELECT 1 FROM t_dict_data parent WHERE parent.dict_value = '" +
                            dictType.getCode() +
                            "' AND parent.is_effect = " + IsEffectEnum.NORMAL.getCode() +
                            " AND parent.status = " + EnableEnum.ENABLE.getCode() +
                            " AND t_dict_data.parent_id = parent.id");

            List<DictData> dictDataList = dictDataMapper.selectList(wrapper);
            return dictDataList.stream()
                    .map(DictData::getDictValue)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取字典数据异常", e);
            return Collections.emptyList();
        }
    }
}
