package com.lanhu.lims.gateway.admin.validation;

import com.lanhu.lims.gateway.admin.enums.DictDataTypeEnum;
import com.lanhu.lims.gateway.admin.utils.validation.DictDataValidationResult;
import com.lanhu.lims.gateway.admin.utils.validation.DictDataValidationUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/********************************
 * @title DictDataValidationTest
 * @package com.lanhu.lims.gateway.admin.validation
 * @description 字典数据验证功能测试
 *
 * <AUTHOR>
 * @date 2025/6/18 16:30
 * @version 0.0.1
 *********************************/
@SpringBootTest
public class DictDataValidationTest {
    @Autowired
    private DictDataValidationUtil dictDataValidationUtil;

    /**
     * 测试获取字典有效值
     */
    @Test
    public void testGetValidDictValues() {
        System.out.println("=== 测试获取字典有效值 ===");
        
        // 测试用户性别
        List<String> genderValues = dictDataValidationUtil.getValidDictValues(DictDataTypeEnum.USER_GENDER);
        System.out.println("用户性别有效值: " + genderValues);
        
        // 测试用户状态
        List<String> statusValues = dictDataValidationUtil.getValidDictValues(DictDataTypeEnum.USER_STATUS);
        System.out.println("用户状态有效值: " + statusValues);
        
        // 测试设备品牌
        List<String> brandValues = dictDataValidationUtil.getValidDictValues(DictDataTypeEnum.EQUIPMENT_BRAND);
        System.out.println("设备品牌有效值: " + brandValues);
    }

    /**
     * 测试单个值验证
     */
    @Test
    public void testValidateSingle() {
        System.out.println("\n=== 测试单个值验证 ===");
        
        // 测试有效值
        DictDataValidationResult result1 = dictDataValidationUtil.validateSingle("男", DictDataTypeEnum.USER_GENDER);
        System.out.println("验证'男': " + result1.isValid() + ", 错误信息: " + result1.getErrorMessage());
        
        // 测试无效值
        DictDataValidationResult result2 = dictDataValidationUtil.validateSingle("无效性别", DictDataTypeEnum.USER_GENDER);
        System.out.println("验证'无效性别': " + result2.isValid() + ", 错误信息: " + result2.getErrorMessage());
        
        // 测试空值
        DictDataValidationResult result3 = dictDataValidationUtil.validateSingle("", DictDataTypeEnum.USER_GENDER);
        System.out.println("验证空值: " + result3.isValid() + ", 错误信息: " + result3.getErrorMessage());
    }

    /**
     * 测试多值验证（逗号分隔）
     */
    @Test
    public void testValidateMultipleComma() {
        System.out.println("\n=== 测试多值验证（逗号分隔） ===");
        
        // 假设有多个有效的设备品牌值
        DictDataValidationResult result1 = dictDataValidationUtil.validateMultiple("品牌1,品牌2", DictDataTypeEnum.EQUIPMENT_BRAND);
        System.out.println("验证'品牌1,品牌2': " + result1.isValid());
        System.out.println("有效值: " + result1.getValidValues());
        System.out.println("无效值: " + result1.getInvalidValues());
        
        // 包含无效值的测试
        DictDataValidationResult result2 = dictDataValidationUtil.validateMultiple("品牌1,无效品牌", DictDataTypeEnum.EQUIPMENT_BRAND);
        System.out.println("验证'品牌1,无效品牌': " + result2.isValid());
        System.out.println("有效值: " + result2.getValidValues());
        System.out.println("无效值: " + result2.getInvalidValues());
    }

    /**
     * 测试自定义分隔符验证
     */
    @Test
    public void testValidateCustomDelimiter() {
        System.out.println("\n=== 测试自定义分隔符验证 ===");
        
        // 分号分隔
        DictDataValidationResult result1 = dictDataValidationUtil.validateMultiple("值1;值2;值3", DictDataTypeEnum.SAMPLE_TYPE, ";");
        System.out.println("分号分隔验证: " + result1.isValid());
        
        // 竖线分隔
        DictDataValidationResult result2 = dictDataValidationUtil.validateMultiple("方法1|方法2", DictDataTypeEnum.INSPECT_METHOD, "|");
        System.out.println("竖线分隔验证: " + result2.isValid());
        
        // 斜杠分隔
        DictDataValidationResult result3 = dictDataValidationUtil.validateMultiple("品牌1/品牌2", DictDataTypeEnum.REAGENT_BRAND, "/");
        System.out.println("斜杠分隔验证: " + result3.isValid());
    }

    /**
     * 测试完整参数验证
     */
    @Test
    public void testValidateWithAllParams() {
        System.out.println("\n=== 测试完整参数验证 ===");
        
        // 不允许空值
        DictDataValidationResult result1 = dictDataValidationUtil.validate("", DictDataTypeEnum.USER_GENDER, ",", false, true, true);
        System.out.println("不允许空值测试: " + result1.isValid() + ", 错误: " + result1.getErrorMessage());
        
        // 不允许重复值
        DictDataValidationResult result2 = dictDataValidationUtil.validate("男,男", DictDataTypeEnum.USER_GENDER, ",", true, false, true);
        System.out.println("不允许重复值测试: " + result2.isValid() + ", 重复值: " + result2.getDuplicateValues());
        
        // 不去除空格
        DictDataValidationResult result3 = dictDataValidationUtil.validate(" 男 , 女 ", DictDataTypeEnum.USER_GENDER, ",", true, true, false);
        System.out.println("不去除空格测试: " + result3.isValid() + ", 解析值: " + result3.getParsedValues());
    }

    /**
     * 测试批量验证
     */
    @Test
    public void testBatchValidate() {
        System.out.println("\n=== 测试批量验证 ===");
        
        // 准备测试数据
        Map<String, String> fieldValues = new HashMap<>();
        fieldValues.put("gender", "男");
        fieldValues.put("status", "启用");
        fieldValues.put("brands", "品牌1,品牌2");
        
        Map<String, DictDataTypeEnum> fieldDictTypes = new HashMap<>();
        fieldDictTypes.put("gender", DictDataTypeEnum.USER_GENDER);
        fieldDictTypes.put("status", DictDataTypeEnum.USER_STATUS);
        fieldDictTypes.put("brands", DictDataTypeEnum.EQUIPMENT_BRAND);
        
        // 批量验证
        Map<String, DictDataValidationResult> results = dictDataValidationUtil.batchValidate(fieldValues, fieldDictTypes, ",");
        
        // 检查结果
        boolean allValid = dictDataValidationUtil.isAllValid(results);
        List<String> invalidFields = dictDataValidationUtil.getInvalidFields(results);
        
        System.out.println("批量验证结果 - 全部有效: " + allValid);
        System.out.println("无效字段: " + invalidFields);
        
        results.forEach((field, result) -> {
            System.out.println(field + ": " + result.isValid() + " - " + result.getErrorMessage());
        });
    }

    /**
     * 测试解析值功能
     */
    @Test
    public void testParseValues() {
        System.out.println("\n=== 测试解析值功能 ===");
        
        // 逗号分隔
        List<String> values1 = dictDataValidationUtil.parseValues("值1,值2,值3", ",", true);
        System.out.println("逗号分隔解析: " + values1);
        
        // 分号分隔
        List<String> values2 = dictDataValidationUtil.parseValues("值1;值2;值3", ";", true);
        System.out.println("分号分隔解析: " + values2);
        
        // 包含空格
        List<String> values3 = dictDataValidationUtil.parseValues(" 值1 , 值2 , 值3 ", ",", true);
        System.out.println("包含空格解析(trim=true): " + values3);
        
        List<String> values4 = dictDataValidationUtil.parseValues(" 值1 , 值2 , 值3 ", ",", false);
        System.out.println("包含空格解析(trim=false): " + values4);
    }

    /**
     * 测试重复值检查
     */
    @Test
    public void testFindDuplicateValues() {
        System.out.println("\n=== 测试重复值检查 ===");
        
        List<String> values1 = List.of("值1", "值2", "值3");
        List<String> duplicates1 = dictDataValidationUtil.findDuplicateValues(values1);
        System.out.println("无重复值: " + duplicates1);
        
        List<String> values2 = List.of("值1", "值2", "值1", "值3", "值2");
        List<String> duplicates2 = dictDataValidationUtil.findDuplicateValues(values2);
        System.out.println("有重复值: " + duplicates2);
    }
}
